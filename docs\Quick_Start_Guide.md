# د ګړندۍ پیل لارښود - Quick Start Guide
## د صرافۍ سیسټم د ۱۸ ورځو پروژه

### د نن ورځې کارونه (Today's Tasks) - ورځ ۱

## ۱. د سیسټم اړتیاوې (System Requirements)

```bash
# د Node.js نصبول (Install Node.js)
# https://nodejs.org/ څخه ډاونلوډ کړئ
node --version  # v18.0.0 یا نوی

# د Git نصبول (Install Git)
git --version

# د VS Code نصبول (Install VS Code)
# https://code.visualstudio.com/
```

## ۲. د پروژې جوړول (Project Creation)

### د Terminal کمانډونه (Terminal Commands):

```bash
# ۱. د اصلي فولډر جوړول
mkdir sarafi-app
cd sarafi-app

# ۲. د Backend جوړول
mkdir backend
cd backend
npm init -y

# ۳. د Backend packages نصبول
npm install express sqlite3 sequelize bcryptjs jsonwebtoken cors dotenv
npm install -D nodemon

# ۴. د Frontend جوړول
cd ../
npx create-react-app frontend --template typescript
cd frontend

# ۵. د Frontend packages نصبول
npm install antd axios react-router-dom @ant-design/icons
npm install -D electron electron-builder concurrently wait-on

# ۶. بیرته اصلي فولډر ته
cd ../
```

## ۳. د Backend بنسټیز فایلونه (Backend Basic Files)

### د فولډر جوړښت جوړول:

```bash
cd backend
mkdir src
mkdir src/models
mkdir src/routes
mkdir src/services
mkdir src/database
mkdir src/middleware
mkdir data
```

### د package.json تنظیمول:

```json
{
  "name": "sarafi-backend",
  "version": "1.0.0",
  "description": "Sarafi Money Exchange Backend",
  "main": "src/app.js",
  "scripts": {
    "start": "node src/app.js",
    "dev": "nodemon src/app.js",
    "sync": "node src/database/sync.js"
  },
  "dependencies": {
    "express": "^4.18.2",
    "sqlite3": "^5.1.6",
    "sequelize": "^6.32.1",
    "bcryptjs": "^2.4.3",
    "jsonwebtoken": "^9.0.1",
    "cors": "^2.8.5",
    "dotenv": "^16.3.1"
  },
  "devDependencies": {
    "nodemon": "^3.0.1"
  }
}
```

### د .env فایل جوړول:

```bash
# backend/.env
PORT=3001
JWT_SECRET=sarafi_secret_key_2024
DB_PATH=./data/sarafi.db
NODE_ENV=development
```

## ۴. د Frontend بنسټیز تنظیمات (Frontend Basic Setup)

### د package.json تنظیمول:

```json
{
  "name": "sarafi-frontend",
  "version": "1.0.0",
  "description": "Sarafi Money Exchange Frontend",
  "main": "public/electron.js",
  "homepage": "./",
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "electron": "electron .",
    "electron-dev": "concurrently \"npm start\" \"wait-on http://localhost:3000 && electron .\"",
    "electron-pack": "electron-builder",
    "preelectron-pack": "npm run build"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.14.1",
    "antd": "^5.7.0",
    "axios": "^1.4.0",
    "@ant-design/icons": "^5.2.5"
  },
  "devDependencies": {
    "electron": "^25.3.0",
    "electron-builder": "^24.6.3",
    "concurrently": "^8.2.0",
    "wait-on": "^7.0.1"
  }
}
```

## ۵. د لومړۍ ورځې کوډ (Day 1 Code)

### Backend App.js:

```javascript
// backend/src/app.js
const express = require('express');
const cors = require('cors');
const path = require('path');
require('dotenv').config();

const app = express();

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// د ساده ټیسټ route
app.get('/api/test', (req, res) => {
    res.json({
        success: true,
        message: 'د صرافۍ سیسټم API کار کوي!',
        timestamp: new Date().toISOString()
    });
});

// د Routes اضافه کول
// app.use('/api/auth', require('./routes/auth'));

const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
    console.log(`🚀 Server running on port ${PORT}`);
    console.log(`📊 Database path: ${process.env.DB_PATH}`);
});

module.exports = app;
```

### Frontend App.tsx:

```typescript
// frontend/src/App.tsx
import React, { useEffect, useState } from 'react';
import { Button, Card, Typography, Space } from 'antd';
import axios from 'axios';
import './App.css';

const { Title, Text } = Typography;

function App() {
  const [connected, setConnected] = useState(false);
  const [message, setMessage] = useState('');

  const testConnection = async () => {
    try {
      const response = await axios.get('http://localhost:3001/api/test');
      setConnected(true);
      setMessage(response.data.message);
    } catch (error) {
      setConnected(false);
      setMessage('د سرور سره اتصال نشته');
    }
  };

  useEffect(() => {
    testConnection();
  }, []);

  return (
    <div className="App" style={{ padding: '50px', direction: 'rtl' }}>
      <Card style={{ maxWidth: 600, margin: '0 auto' }}>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <Title level={2} style={{ textAlign: 'center' }}>
            د صرافۍ سیسټم
          </Title>
          
          <div style={{ textAlign: 'center' }}>
            <Text strong>د سرور حالت: </Text>
            <Text type={connected ? 'success' : 'danger'}>
              {connected ? 'وصل دی' : 'وصل نه دی'}
            </Text>
          </div>
          
          <div style={{ textAlign: 'center' }}>
            <Text>{message}</Text>
          </div>
          
          <Button 
            type="primary" 
            onClick={testConnection}
            style={{ width: '100%' }}
          >
            د اتصال ټیسټ
          </Button>
        </Space>
      </Card>
    </div>
  );
}

export default App;
```

### Frontend App.css:

```css
/* frontend/src/App.css */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap');

* {
  font-family: 'Noto Sans Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.App {
  direction: rtl;
  text-align: right;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.ant-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
}

.ant-typography {
  direction: rtl;
}
```

## ۶. د Electron تنظیمات (Electron Setup)

### د Electron main فایل:

```javascript
// frontend/public/electron.js
const { app, BrowserWindow } = require('electron');
const path = require('path');
const isDev = require('electron-is-dev');

function createWindow() {
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    },
    title: 'د صرافۍ سیسټم - Sarafi System'
  });

  mainWindow.loadURL(
    isDev
      ? 'http://localhost:3000'
      : `file://${path.join(__dirname, '../build/index.html')}`
  );

  if (isDev) {
    mainWindow.webContents.openDevTools();
  }
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
```

## ۷. د لومړۍ ورځې ټیسټ (Day 1 Testing)

### د Backend پیل کول:

```bash
cd backend
npm run dev
```

### د Frontend پیل کول (نوي terminal کې):

```bash
cd frontend
npm start
```

### د Electron ټیسټ (نوي terminal کې):

```bash
cd frontend
npm run electron-dev
```

## ۸. د بریالیتوب نښې (Success Indicators)

✅ **Backend**: `http://localhost:3001/api/test` کار کوي
✅ **Frontend**: `http://localhost:3000` کار کوي  
✅ **Electron**: Desktop app پرانیزي
✅ **د پښتو متن**: سمه ښکاري
✅ **API Connection**: Frontend او Backend اتصال لري

## ۹. د سبا ورځې تیاري (Tomorrow's Preparation)

### د ډیټابیس فایلونو جوړول:

```bash
# دا فایلونه سبا جوړ کړئ
touch backend/src/database/connection.js
touch backend/src/database/sync.js
touch backend/src/models/index.js
touch backend/src/models/User.js
touch backend/src/models/Customer.js
```

## ۱۰. د مرستې سرچینې (Help Resources)

- **د Node.js لارښود**: https://nodejs.org/en/docs/
- **د React لارښود**: https://react.dev/
- **د Ant Design**: https://ant.design/
- **د Sequelize**: https://sequelize.org/
- **د پښتو فونټ**: Google Fonts - Noto Sans Arabic

---

## د ورځني کار بشپړول (Daily Completion Checklist)

- [ ] د پروژې فولډر جوړ شو
- [ ] Backend او Frontend setup شو
- [ ] د اړینو packages نصب شول
- [ ] د لومړني ټیسټ بریالي و
- [ ] د پښتو UI کار کوي
- [ ] د سبا ورځې فایلونه چمتو دي

که دا ټول کارونه بشپړ شول، نو تاسو د لومړۍ ورځې موخه ترلاسه کړې!

**د سبا ورځې موخه**: د ډیټابیس ټیبلونه او د کاروونکو سیسټم جوړول
