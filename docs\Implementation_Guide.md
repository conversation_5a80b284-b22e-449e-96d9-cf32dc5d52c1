# د پلي کولو لارښود - Sarafi Implementation Guide

## د لومړۍ ورځې تفصیلي کارونه (Day 1 Detailed Tasks)

### ۱. د پروژې جوړول (Project Setup)

```bash
# د اصلي فولډر جوړول
mkdir sarafi-app
cd sarafi-app

# د Backend جوړول
mkdir backend
cd backend
npm init -y

# د اړینو packages نصبول
npm install express sqlite3 sequelize bcryptjs jsonwebtoken cors dotenv
npm install -D nodemon

# د Frontend جوړول
cd ../
npx create-react-app frontend --template typescript
cd frontend

# د Frontend packages نصبول
npm install antd axios react-router-dom @types/node
npm install -D electron electron-builder concurrently wait-on
```

### ۲. د Backend بنسټیز جوړښت

```javascript
// backend/src/app.js
const express = require('express');
const cors = require('cors');
const path = require('path');
require('dotenv').config();

const app = express();

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Routes
app.use('/api/auth', require('./routes/auth'));
app.use('/api/users', require('./routes/users'));
app.use('/api/customers', require('./routes/customers'));
app.use('/api/currencies', require('./routes/currencies'));
app.use('/api/daybooks', require('./routes/daybooks'));
app.use('/api/transactions', require('./routes/transactions'));
app.use('/api/ledger', require('./routes/ledger'));
app.use('/api/admin', require('./routes/admin'));

const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
});

module.exports = app;
```

### ۳. د ډیټابیس تنظیمات

```javascript
// backend/src/database/connection.js
const { Sequelize } = require('sequelize');
const path = require('path');

const sequelize = new Sequelize({
    dialect: 'sqlite',
    storage: path.join(__dirname, '../../data/sarafi.db'),
    logging: false,
    define: {
        timestamps: true,
        underscored: true
    }
});

module.exports = sequelize;
```

```javascript
// backend/src/models/index.js
const sequelize = require('../database/connection');
const User = require('./User')(sequelize);
const Customer = require('./Customer')(sequelize);
const Currency = require('./Currency')(sequelize);
const Daybook = require('./Daybook')(sequelize);
const Transaction = require('./Transaction')(sequelize);
const CustomerBalance = require('./CustomerBalance')(sequelize);
const SystemSetting = require('./SystemSetting')(sequelize);
const AuditLog = require('./AuditLog')(sequelize);

// Associations
User.hasMany(Customer, { foreignKey: 'created_by' });
Customer.belongsTo(User, { foreignKey: 'created_by' });

User.hasMany(Daybook, { foreignKey: 'created_by' });
Daybook.belongsTo(User, { foreignKey: 'created_by' });

User.hasMany(Transaction, { foreignKey: 'created_by' });
Transaction.belongsTo(User, { foreignKey: 'created_by' });

Customer.hasMany(Transaction, { foreignKey: 'customer_id' });
Transaction.belongsTo(Customer, { foreignKey: 'customer_id' });

Currency.hasMany(Transaction, { foreignKey: 'currency_id' });
Transaction.belongsTo(Currency, { foreignKey: 'currency_id' });

Daybook.hasMany(Transaction, { foreignKey: 'daybook_id' });
Transaction.belongsTo(Daybook, { foreignKey: 'daybook_id' });

Customer.hasMany(CustomerBalance, { foreignKey: 'customer_id' });
CustomerBalance.belongsTo(Customer, { foreignKey: 'customer_id' });

Currency.hasMany(CustomerBalance, { foreignKey: 'currency_id' });
CustomerBalance.belongsTo(Currency, { foreignKey: 'currency_id' });

module.exports = {
    sequelize,
    User,
    Customer,
    Currency,
    Daybook,
    Transaction,
    CustomerBalance,
    SystemSetting,
    AuditLog
};
```

## د دویمې ورځې کارونه (Day 2 Tasks)

### ۱. د Models جوړول

```javascript
// backend/src/models/User.js
const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
    return sequelize.define('User', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        username: {
            type: DataTypes.STRING(50),
            unique: true,
            allowNull: false
        },
        password_hash: {
            type: DataTypes.STRING(255),
            allowNull: false
        },
        full_name: {
            type: DataTypes.STRING(100),
            allowNull: false
        },
        mobile_number: {
            type: DataTypes.STRING(20)
        },
        role: {
            type: DataTypes.ENUM('super_admin', 'admin', 'user'),
            defaultValue: 'user'
        },
        permissions: {
            type: DataTypes.JSON,
            defaultValue: {}
        },
        is_active: {
            type: DataTypes.BOOLEAN,
            defaultValue: true
        }
    }, {
        tableName: 'users',
        timestamps: true
    });
};
```

```javascript
// backend/src/models/Customer.js
const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
    return sequelize.define('Customer', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        customer_code: {
            type: DataTypes.STRING(20),
            unique: true,
            allowNull: false
        },
        full_name: {
            type: DataTypes.STRING(100),
            allowNull: false
        },
        mobile_number: {
            type: DataTypes.STRING(20)
        },
        address: {
            type: DataTypes.TEXT
        },
        loan_limit: {
            type: DataTypes.DECIMAL(15, 2),
            defaultValue: 500000.00
        },
        is_active: {
            type: DataTypes.BOOLEAN,
            defaultValue: true
        },
        created_by: {
            type: DataTypes.INTEGER,
            allowNull: false
        }
    }, {
        tableName: 'customers',
        timestamps: true
    });
};
```

### ۲. د Database Sync

```javascript
// backend/src/database/sync.js
const { sequelize } = require('../models');

async function syncDatabase() {
    try {
        await sequelize.authenticate();
        console.log('Database connection established successfully.');
        
        await sequelize.sync({ force: false });
        console.log('Database synchronized successfully.');
        
        // د لومړني Super Admin جوړول
        await createDefaultAdmin();
        
    } catch (error) {
        console.error('Unable to connect to the database:', error);
    }
}

async function createDefaultAdmin() {
    const { User } = require('../models');
    const bcrypt = require('bcryptjs');
    
    const adminExists = await User.findOne({ where: { role: 'super_admin' } });
    
    if (!adminExists) {
        const hashedPassword = await bcrypt.hash('admin123', 12);
        
        await User.create({
            username: 'admin',
            password_hash: hashedPassword,
            full_name: 'د سیسټم مدیر',
            role: 'super_admin',
            permissions: {
                all: true
            }
        });
        
        console.log('Default admin user created: admin/admin123');
    }
}

module.exports = { syncDatabase };
```

## د دریمې ورځې کارونه (Day 3 Tasks)

### ۱. د Authentication سیسټم

```javascript
// backend/src/routes/auth.js
const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { User } = require('../models');

const router = express.Router();

// د ننه کیدل (Login)
router.post('/login', async (req, res) => {
    try {
        const { username, password } = req.body;
        
        // د کاروونکي موندل
        const user = await User.findOne({ 
            where: { username, is_active: true } 
        });
        
        if (!user) {
            return res.status(401).json({
                success: false,
                message: 'د کاروونکي نوم یا پاسورډ غلط دی'
            });
        }
        
        // د پاسورډ تصدیق
        const isValidPassword = await bcrypt.compare(password, user.password_hash);
        
        if (!isValidPassword) {
            return res.status(401).json({
                success: false,
                message: 'د کاروونکي نوم یا پاسورډ غلط دی'
            });
        }
        
        // د JWT Token جوړول
        const token = jwt.sign(
            { 
                userId: user.id, 
                username: user.username,
                role: user.role 
            },
            process.env.JWT_SECRET || 'sarafi_secret_key',
            { expiresIn: '24h' }
        );
        
        res.json({
            success: true,
            message: 'بریالي ننه کیدل',
            data: {
                token,
                user: {
                    id: user.id,
                    username: user.username,
                    full_name: user.full_name,
                    role: user.role,
                    permissions: user.permissions
                }
            }
        });
        
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'د سرور غلطي',
            error: error.message
        });
    }
});

module.exports = router;
```

### ۲. د Frontend بنسټیز جوړښت

```typescript
// frontend/src/App.tsx
import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import ps_AF from 'antd/locale/ps_AF';
import './App.css';

import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import Customers from './pages/Customers';
import Daybook from './pages/Daybook';
import Ledger from './pages/Ledger';
import Settings from './pages/Settings';

function App() {
  return (
    <ConfigProvider 
      locale={ps_AF}
      direction="rtl"
      theme={{
        token: {
          fontFamily: 'Noto Sans Pashto, Arial, sans-serif',
        },
      }}
    >
      <Router>
        <div className="App">
          <Routes>
            <Route path="/login" element={<Login />} />
            <Route path="/" element={<Dashboard />} />
            <Route path="/customers" element={<Customers />} />
            <Route path="/daybook" element={<Daybook />} />
            <Route path="/ledger" element={<Ledger />} />
            <Route path="/settings" element={<Settings />} />
          </Routes>
        </div>
      </Router>
    </ConfigProvider>
  );
}

export default App;
```

### ۳. د Login صفحه

```typescript
// frontend/src/pages/Login.tsx
import React, { useState } from 'react';
import { Form, Input, Button, Card, message } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import './Login.css';

interface LoginForm {
  username: string;
  password: string;
}

const Login: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const onFinish = async (values: LoginForm) => {
    setLoading(true);
    try {
      const response = await axios.post('http://localhost:3001/api/auth/login', values);
      
      if (response.data.success) {
        localStorage.setItem('token', response.data.data.token);
        localStorage.setItem('user', JSON.stringify(response.data.data.user));
        message.success(response.data.message);
        navigate('/');
      }
    } catch (error: any) {
      message.error(error.response?.data?.message || 'د ننه کیدو کې ستونزه');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-container">
      <Card 
        title="د صرافۍ سیسټم ته ننه کیدل" 
        className="login-card"
        style={{ direction: 'rtl' }}
      >
        <Form
          name="login"
          onFinish={onFinish}
          autoComplete="off"
          layout="vertical"
        >
          <Form.Item
            label="د کاروونکي نوم"
            name="username"
            rules={[{ required: true, message: 'د کاروونکي نوم ولیکئ!' }]}
          >
            <Input 
              prefix={<UserOutlined />} 
              placeholder="د کاروونکي نوم"
              size="large"
            />
          </Form.Item>

          <Form.Item
            label="پاسورډ"
            name="password"
            rules={[{ required: true, message: 'پاسورډ ولیکئ!' }]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="پاسورډ"
              size="large"
            />
          </Form.Item>

          <Form.Item>
            <Button 
              type="primary" 
              htmlType="submit" 
              loading={loading}
              size="large"
              block
            >
              ننه کیدل
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default Login;
```

## د CSS تنظیمات د پښتو لپاره

```css
/* frontend/src/App.css */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap');

* {
  font-family: 'Noto Sans Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.App {
  direction: rtl;
  text-align: right;
}

/* د پښتو متن لپاره */
.pashto-text {
  font-family: 'Noto Sans Arabic', Arial, sans-serif;
  direction: rtl;
  text-align: right;
  line-height: 1.6;
}

/* د Login صفحې لپاره */
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-card {
  width: 400px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
}

.login-card .ant-card-head-title {
  text-align: center;
  font-size: 18px;
  font-weight: 600;
}
```

## د ورځني کار د بشپړولو لیست

### ورځ ۱ ✅
- [x] د پروژې فولډر جوړول
- [x] Backend او Frontend setup
- [x] د اړینو packages نصبول
- [x] د بنسټیز فایلونو جوړول

### ورځ ۲ ✅
- [x] د ډیټابیس models جوړول
- [x] د associations تنظیمول
- [x] د database sync کول
- [x] د لومړني admin جوړول

### ورځ ۳ 🔄
- [x] د authentication API جوړول
- [x] د login صفحې جوړول
- [x] د JWT tokens تنظیمول
- [x] د پښتو UI تنظیمول

دا لارښود ستاسو سره د لومړیو درې ورځو لپاره تفصیلي کارونه ورکوي. که ستاسو د نورو ورځو د تفصیلاتو ته اړتیا وي، زه کولی شم د هرې ورځې لپاره بیلابیل لارښودونه جوړ کړم.
