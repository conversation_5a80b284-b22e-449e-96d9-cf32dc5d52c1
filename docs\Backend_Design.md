# Backend Design Document

## Sarafi Money Exchange System

### Document Information

- **Project Name**: Sarafi Money Exchange System Backend
- **Version**: 1.0
- **Date**: August 6, 2025
- **Architecture**: Layered Architecture with MVC Pattern

---

## 1. System Architecture Overview

### 1.1 Architecture Pattern

The system follows a **Layered Architecture** with the following layers:

1. **Presentation Layer** (Controllers/API)
2. **Business Logic Layer** (Services)
3. **Data Access Layer** (Repositories)
4. **Database Layer** (SQLite/PostgreSQL)

### 1.2 Technology Stack

- **Backend Framework**: Node.js with Express.js / .NET Core / Python Django
- **Database**: SQLite (for single-user) / PostgreSQL (for multi-user)
- **ORM**: Sequelize / Entity Framework / Django ORM
- **Authentication**: JWT tokens
- **File Storage**: Local file system for backups and documents

---

## 2. Database Design

### 2.1 Entity Relationship Diagram (ERD)

```
Users ||--o{ Transactions : creates
Users ||--o{ AuditLogs : performs
Customers ||--o{ Transactions : involved_in
Currencies ||--o{ Transactions : uses
Daybooks ||--o{ Transactions : contains
Customers ||--o{ CustomerBalances : has
Currencies ||--o{ CustomerBalances : denominated_in
```

### 2.2 Database Tables

#### 2.2.1 Users Table

```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    mobile_number VARCHAR(20),
    role ENUM('super_admin', 'admin', 'user') DEFAULT 'user',
    permissions JSON,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 2.2.2 Customers Table

```sql
CREATE TABLE customers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_code VARCHAR(20) UNIQUE NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    mobile_number VARCHAR(20),
    address TEXT,
    loan_limit DECIMAL(15,2) DEFAULT 500000.00,
    is_active BOOLEAN DEFAULT true,
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 2.2.3 Currencies Table

```sql
CREATE TABLE currencies (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    currency_code VARCHAR(10) UNIQUE NOT NULL,
    currency_name VARCHAR(50) NOT NULL,
    color_code VARCHAR(7), -- Hex color code
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 2.2.4 Daybooks Table

```sql
CREATE TABLE daybooks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    daybook_date DATE NOT NULL,
    page_number INTEGER NOT NULL,
    status ENUM('draft', 'finalized') DEFAULT 'draft',
    created_by INTEGER REFERENCES users(id),
    finalized_by INTEGER REFERENCES users(id),
    finalized_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(daybook_date, page_number)
);
```

#### 2.2.5 Transactions Table

```sql
CREATE TABLE transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    daybook_id INTEGER REFERENCES daybooks(id),
    transaction_type ENUM('jamah', 'naam') NOT NULL,
    index_number INTEGER NOT NULL,
    customer_id INTEGER REFERENCES customers(id),
    currency_id INTEGER REFERENCES currencies(id),
    amount DECIMAL(15,2) NOT NULL,
    description TEXT,
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 2.2.6 Customer Balances Table

```sql
CREATE TABLE customer_balances (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_id INTEGER REFERENCES customers(id),
    currency_id INTEGER REFERENCES currencies(id),
    jamah_total DECIMAL(15,2) DEFAULT 0.00,
    naam_total DECIMAL(15,2) DEFAULT 0.00,
    balance DECIMAL(15,2) DEFAULT 0.00,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(customer_id, currency_id)
);
```

#### 2.2.7 System Settings Table

```sql
CREATE TABLE system_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_name VARCHAR(100),
    responsible_person VARCHAR(100),
    mobile_number VARCHAR(20),
    business_address TEXT,
    logo_path VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 2.2.8 Audit Logs Table

```sql
CREATE TABLE audit_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER REFERENCES users(id),
    action VARCHAR(50) NOT NULL,
    table_name VARCHAR(50),
    record_id INTEGER,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

## 3. API Design

### 3.1 Authentication Endpoints

```
POST /api/auth/login
POST /api/auth/logout
POST /api/auth/refresh-token
GET  /api/auth/profile
```

### 3.2 User Management Endpoints

```
GET    /api/users              # List all users (super admin only)
POST   /api/users              # Create new user (super admin only)
GET    /api/users/:id          # Get user details
PUT    /api/users/:id          # Update user
DELETE /api/users/:id          # Delete user (super admin only)
PUT    /api/users/:id/permissions # Update user permissions
```

### 3.3 Customer Management Endpoints

```
GET    /api/customers          # List customers with pagination
POST   /api/customers          # Create new customer
GET    /api/customers/:id      # Get customer details
PUT    /api/customers/:id      # Update customer
DELETE /api/customers/:id      # Delete customer
GET    /api/customers/search   # Search customers by name/mobile
```

### 3.4 Currency Management Endpoints

```
GET    /api/currencies         # List all currencies
POST   /api/currencies         # Create new currency
PUT    /api/currencies/:id     # Update currency
DELETE /api/currencies/:id     # Delete currency
```

### 3.5 Daybook Endpoints

```
GET    /api/daybooks           # List daybooks with filters
POST   /api/daybooks           # Create new daybook
GET    /api/daybooks/:id       # Get daybook details
PUT    /api/daybooks/:id       # Update daybook (if not finalized)
POST   /api/daybooks/:id/finalize # Finalize daybook
GET    /api/daybooks/:id/print # Generate printable version
```

### 3.6 Transaction Endpoints

```
GET    /api/transactions       # List transactions with filters
POST   /api/transactions       # Create new transaction
GET    /api/transactions/:id   # Get transaction details
PUT    /api/transactions/:id   # Update transaction
DELETE /api/transactions/:id   # Delete transaction
```

### 3.7 Ledger Endpoints

```
GET    /api/ledger/customers/:id # Get customer ledger
GET    /api/ledger/summary      # Get overall summary
GET    /api/ledger/balances     # Get all customer balances
POST   /api/ledger/export       # Export ledger to PDF
```

### 3.8 System Administration Endpoints

```
POST   /api/admin/backup        # Create system backup
POST   /api/admin/restore       # Restore from backup
POST   /api/admin/reset         # Reset system to zero state
GET    /api/admin/audit-logs    # Get audit logs
GET    /api/admin/settings      # Get system settings
PUT    /api/admin/settings      # Update system settings
```

---

## 4. Business Logic Layer

### 4.1 Service Classes

#### 4.1.1 AuthService

- User authentication and authorization
- JWT token management
- Password hashing and validation
- Session management

#### 4.1.2 UserService

- User CRUD operations
- Permission management
- User validation and business rules

#### 4.1.3 CustomerService

- Customer registration and management
- Customer ID generation
- Loan limit validation
- Customer search functionality

#### 4.1.4 CurrencyService

- Currency management
- Color code validation
- Currency code uniqueness

#### 4.1.5 DaybookService

- Daybook creation and management
- Page number auto-increment
- Finalization logic
- Balance calculations

#### 4.1.6 TransactionService

- Transaction CRUD operations
- Balance updates
- Transaction validation
- Dual-entry bookkeeping logic

#### 4.1.7 LedgerService

- Customer balance calculations
- Ledger report generation
- Transaction history compilation
- PDF export functionality

#### 4.1.8 BackupService

- Database backup creation
- Backup restoration
- Data validation during restore
- Backup file management

---

## 5. Data Access Layer

### 5.1 Repository Pattern

Each entity will have a corresponding repository class:

- UserRepository
- CustomerRepository
- CurrencyRepository
- DaybookRepository
- TransactionRepository
- AuditLogRepository

### 5.2 Repository Interface Example

```typescript
interface ICustomerRepository {
  findAll(filters?: CustomerFilters): Promise<Customer[]>;
  findById(id: number): Promise<Customer | null>;
  create(customer: CreateCustomerDto): Promise<Customer>;
  update(id: number, customer: UpdateCustomerDto): Promise<Customer>;
  delete(id: number): Promise<boolean>;
  findByMobile(mobile: string): Promise<Customer | null>;
  generateCustomerCode(): Promise<string>;
}
```

---

## 6. Security Considerations

### 6.1 Authentication & Authorization

- JWT-based authentication
- Role-based access control (RBAC)
- Permission-based feature access
- Session timeout management

### 6.2 Data Security

- Password hashing using bcrypt
- SQL injection prevention through parameterized queries
- Input validation and sanitization
- Audit logging for all operations

### 6.3 API Security

- Rate limiting
- CORS configuration
- Request validation middleware
- Error handling without information leakage

---

## 7. Performance Considerations

### 7.1 Database Optimization

- Proper indexing on frequently queried columns
- Connection pooling
- Query optimization
- Pagination for large datasets

### 7.2 Caching Strategy

- In-memory caching for frequently accessed data
- Cache invalidation strategies
- Session caching

### 7.3 Scalability

- Horizontal scaling capability
- Load balancing considerations
- Database sharding potential

---

## 8. Error Handling

### 8.1 Error Types

- Validation errors
- Business logic errors
- Database errors
- Authentication errors
- System errors

### 8.2 Error Response Format

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field": "mobile_number",
      "reason": "Invalid format"
    }
  },
  "timestamp": "2025-08-06T10:30:00Z"
}
```

---

## 9. Logging and Monitoring

### 9.1 Application Logging

- Request/response logging
- Error logging with stack traces
- Performance metrics
- User activity logging

### 9.2 Audit Trail

- All CRUD operations logged
- User authentication events
- System configuration changes
- Data export/backup activities

---

## 10. Deployment Architecture

### 10.1 Single-User Deployment

- Desktop application with embedded SQLite
- Local file system for backups
- Standalone executable

### 10.2 Multi-User Deployment

- Client-server architecture
- PostgreSQL database server
- Web-based interface
- Network file storage for backups

---

## 11. Implementation Recommendations

### 11.1 Technology Stack Recommendations

#### Option 1: Node.js Stack

- **Backend**: Node.js with Express.js
- **Database**: SQLite (single-user) / PostgreSQL (multi-user)
- **ORM**: Sequelize or Prisma
- **Authentication**: Passport.js with JWT
- **File Processing**: Multer for uploads, PDFKit for PDF generation
- **Testing**: Jest with Supertest

#### Option 2: .NET Core Stack

- **Backend**: ASP.NET Core Web API
- **Database**: SQLite / SQL Server / PostgreSQL
- **ORM**: Entity Framework Core
- **Authentication**: ASP.NET Core Identity with JWT
- **File Processing**: iTextSharp for PDF generation
- **Testing**: xUnit with ASP.NET Core Test Host

#### Option 3: Python Stack

- **Backend**: Django REST Framework or FastAPI
- **Database**: SQLite / PostgreSQL
- **ORM**: Django ORM or SQLAlchemy
- **Authentication**: Django REST Auth or FastAPI Security
- **File Processing**: ReportLab for PDF generation
- **Testing**: pytest with Django Test Client

### 11.2 Development Phases

#### Phase 1: Core Infrastructure (2-3 weeks)

- Database setup and migrations
- User authentication and authorization
- Basic CRUD operations for all entities
- API documentation setup

#### Phase 2: Business Logic Implementation (3-4 weeks)

- Customer management system
- Currency management
- Basic transaction recording
- User permission system

#### Phase 3: Daybook System (2-3 weeks)

- Daybook creation and management
- Transaction entry interface
- Balance calculations
- Finalization workflow

#### Phase 4: Reporting and Ledger (2-3 weeks)

- Customer ledger generation
- PDF export functionality
- Transaction history reports
- Summary dashboards

#### Phase 5: System Administration (1-2 weeks)

- Backup and restore functionality
- System settings management
- Audit logging
- Performance optimization

#### Phase 6: Testing and Deployment (1-2 weeks)

- Comprehensive testing
- Security audit
- Performance testing
- Deployment setup

### 11.3 Security Implementation Checklist

- [ ] Password hashing with bcrypt (minimum 12 rounds)
- [ ] JWT token expiration and refresh mechanism
- [ ] Input validation and sanitization
- [ ] SQL injection prevention
- [ ] XSS protection
- [ ] CSRF protection
- [ ] Rate limiting implementation
- [ ] Audit logging for all operations
- [ ] Role-based access control
- [ ] Secure file upload handling

### 11.4 Performance Optimization

- [ ] Database indexing strategy
- [ ] Query optimization
- [ ] Connection pooling
- [ ] Caching implementation
- [ ] Pagination for large datasets
- [ ] Lazy loading for related data
- [ ] Background job processing
- [ ] Memory usage monitoring

---

This backend design provides a solid foundation for the Sarafi Money Exchange System with scalability, security, and maintainability in mind. The modular architecture allows for easy testing, maintenance, and future enhancements.
