# د ۱۸ ورځو کې د صرافۍ سیسټم جوړولو پلان
# 18-Day Sarafi System Development Plan

## د پروژې لنډیز (Project Overview)
- **موده (Duration)**: ۱۸ ورځې
- **ژبه (Language)**: پښتو
- **ټیکنالوژي (Technology)**: Node.js + React + SQLite
- **ډول (Type)**: Desktop Application

---

## د ورځو تقسیمات (Daily Breakdown)

### هفته ۱ - بنسټیز جوړښت (Week 1 - Foundation)

#### ورځ ۱-۲: د پروژې تنظیمات (Days 1-2: Project Setup)
**موخه**: د پروژې بنسټیز جوړښت

**کارونه**:
- [ ] Node.js او React پروژه جوړول
- [ ] د ډیټابیس سکیما جوړول (SQLite)
- [ ] د پښتو فونټ او UI کتابتونونه تنظیمول
- [ ] د پروژې فولډر جوړښت
- [ ] Git repository تنظیمول

**فایلونه چې جوړ کړئ**:
```
sarafi-app/
├── backend/
│   ├── src/
│   │   ├── models/
│   │   ├── routes/
│   │   ├── services/
│   │   └── database/
├── frontend/
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   ├── utils/
│   │   └── assets/fonts/
└── docs/
```

#### ورځ ۳-۴: د ډیټابیس جوړول (Days 3-4: Database Setup)
**موخه**: د ډیټابیس ټیبلونه او اړیکې

**کارونه**:
- [ ] د ۸ ټیبلونو جوړول
- [ ] د ډیټابیس migration فایلونه
- [ ] د نمونه ډیټا ورکول (seed data)
- [ ] د ډیټابیس ټیسټ کول

**ټیبلونه**:
1. کاروونکي (users)
2. پیرودونکي (customers) 
3. اسعارو (currencies)
4. ورځپاڼه (daybooks)
5. راکړه ورکړه (transactions)
6. د پیرودونکو بیلانس (customer_balances)
7. د سیسټم تنظیمات (system_settings)
8. د عملیاتو ثبت (audit_logs)

#### ورځ ۵-۶: د کاروونکو سیسټم (Days 5-6: User System)
**موخه**: د کاروونکو ثبت او د دننه کیدو سیسټم

**کارونه**:
- [ ] د کاروونکو ثبت API
- [ ] د ننه کیدو سیسټم (Login)
- [ ] JWT tokens
- [ ] د اجازو سیسټم (Permissions)
- [ ] د پښتو login صفحه

#### ورځ ۷: د پیرودونکو مدیریت (Day 7: Customer Management)
**موخه**: د پیرودونکو ثبت او مدیریت

**کارونه**:
- [ ] د پیرودونکو ثبت API
- [ ] د پیرودونکو لیست
- [ ] د پیرودونکو ایډیټ کول
- [ ] د پیرودونکو ID اتومات جوړول

### هفته ۲ - اصلي فیچرونه (Week 2 - Core Features)

#### ورځ ۸-۹: د اسعارو سیسټم (Days 8-9: Currency System)
**موخه**: د مختلفو اسعارو ملاتړ

**کارونه**:
- [ ] د اسعارو ثبت API
- [ ] د رنګونو سیسټم
- [ ] د اسعارو لیست
- [ ] د اسعارو validation

#### ورځ ۱۰-۱۲: د ورځپاڼې سیسټم (Days 10-12: Daybook System)
**موخه**: د ورځني راکړه ورکړو ثبت

**کارونه**:
- [ ] د ورځپاڼې جوړول API
- [ ] د جمع او نام interface
- [ ] د راکړه ورکړو ثبت
- [ ] د بیلانس محاسبه
- [ ] د ورځپاڼې بندول (Finalize)

**د UI ډیزاین**:
```
┌─────────────────────────────────────────┐
│           د ورځپاڼې صفحه                │
├─────────────────┬───────────────────────┤
│      جمع        │         نام           │
│   (Credits)     │      (Debits)         │
├─────────────────┼───────────────────────┤
│ پیرودونکی:      │ پیرودونکی:            │
│ اسعار:          │ اسعار:               │
│ مقدار:          │ مقدار:               │
│ تفصیل:          │ تفصیل:               │
└─────────────────┴───────────────────────┘
```

#### ورځ ۱۳-۱۴: د کتاب سیسټم (Days 13-14: Ledger System)
**موخه**: د پیرودونکو د حساب کتاب

**کارونه**:
- [ ] د پیرودونکو تاریخچه
- [ ] د بیلانس محاسبه
- [ ] د فلټر سیسټم
- [ ] د PDF export

### هفته ۳ - بشپړول او ټیسټ (Week 3 - Completion & Testing)

#### ورځ ۱۵: د راپورونو سیسټم (Day 15: Reporting System)
**موخه**: د مختلفو راپورونو جوړول

**کارونه**:
- [ ] د ورځپاڼې پرنټ
- [ ] د کتاب PDF
- [ ] د لنډیز راپور
- [ ] د پښتو PDF ملاتړ

#### ورځ ۱۶: د بیک اپ سیسټم (Day 16: Backup System)
**موخه**: د ډیټا ساتنه او بیرته راوړل

**کارونه**:
- [ ] د ډیټابیس بیک اپ
- [ ] د بیک اپ بیرته راوړل
- [ ] د سیسټم ریسیټ
- [ ] د فایل مدیریت

#### ورځ ۱۷: ټیسټ او د غلطیو سمون (Day 17: Testing & Bug Fixes)
**موخه**: د سیسټم بشپړ ټیسټ

**کارونه**:
- [ ] د ټولو فیچرونو ټیسټ
- [ ] د غلطیو موندل او سمون
- [ ] د کارکرد ټیسټ
- [ ] د امنیت ټیسټ

#### ورځ ۱۸: وروستي تنظیمات (Day 18: Final Setup)
**موخه**: د پروژې بشپړول

**کارونه**:
- [ ] د executable فایل جوړول
- [ ] د نصب کولو لارښود
- [ ] د کاروونکو لارښود
- [ ] وروستي ټیسټ

---

## د ټیکنالوژۍ انتخاب (Technology Stack)

### Backend
```javascript
// Package.json
{
  "dependencies": {
    "express": "^4.18.2",
    "sqlite3": "^5.1.6",
    "sequelize": "^6.32.1",
    "bcryptjs": "^2.4.3",
    "jsonwebtoken": "^9.0.1",
    "cors": "^2.8.5"
  }
}
```

### Frontend
```javascript
// Package.json
{
  "dependencies": {
    "react": "^18.2.0",
    "react-router-dom": "^6.14.1",
    "axios": "^1.4.0",
    "antd": "^5.7.0",
    "jspdf": "^2.5.1",
    "electron": "^25.3.0"
  }
}
```

---

## د پښتو UI کمپوننټونه (Pashto UI Components)

### د بنسټیز کمپوننټونو لیست
1. **د ننه کیدو صفحه** (Login Page)
2. **اصلي مینو** (Main Menu)
3. **د پیرودونکو لیست** (Customer List)
4. **د ورځپاڼې صفحه** (Daybook Page)
5. **د کتاب صفحه** (Ledger Page)
6. **د تنظیماتو صفحه** (Settings Page)

### د فونټ تنظیمات
```css
@font-face {
  font-family: 'Pashto';
  src: url('./assets/fonts/NotoSansPashto.woff2');
}

.pashto-text {
  font-family: 'Pashto', Arial, sans-serif;
  direction: rtl;
  text-align: right;
}
```

---

## د هرې ورځې تفصیلي کارونه (Detailed Daily Tasks)

### ورځ ۱: د پروژې پیل
```bash
# 1. د پروژې فولډر جوړول
mkdir sarafi-app
cd sarafi-app

# 2. Backend setup
mkdir backend
cd backend
npm init -y
npm install express sqlite3 sequelize bcryptjs jsonwebtoken cors

# 3. Frontend setup
cd ../
npx create-react-app frontend
cd frontend
npm install antd axios react-router-dom

# 4. د پښتو فونټ ډاونلوډ
# Google Fonts څخه Noto Sans Pashto
```

### ورځ ۲: د ډیټابیس سکیما
```javascript
// backend/src/models/User.js
const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  return sequelize.define('User', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    username: {
      type: DataTypes.STRING(50),
      unique: true,
      allowNull: false
    },
    password_hash: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    full_name: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    mobile_number: DataTypes.STRING(20),
    role: {
      type: DataTypes.ENUM('super_admin', 'admin', 'user'),
      defaultValue: 'user'
    },
    permissions: DataTypes.JSON,
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    }
  });
};
```

---

## د هر هفتې موخې (Weekly Goals)

### هفته ۱ موخې:
- ✅ د پروژې بنسټیز جوړښت
- ✅ د ډیټابیس تنظیمات
- ✅ د کاروونکو سیسټم
- ✅ د پیرودونکو مدیریت

### هفته ۲ موخې:
- ✅ د اسعارو سیسټم
- ✅ د ورځپاڼې مکمل سیسټم
- ✅ د کتاب سیسټم
- ✅ د پښتو UI

### هفته ۳ موخې:
- ✅ د راپورونو سیسټم
- ✅ د بیک اپ سیسټم
- ✅ مکمل ټیسټ
- ✅ د پروژې بشپړول

---

## د کار د وخت ویش (Work Schedule)

### ورځني کار: ۸-۱۰ ساعته
- **سهار**: ۹:۰۰ - ۱۲:۰۰ (۳ ساعته)
- **ماسپښین**: ۱:۰۰ - ۵:۰۰ (۴ ساعته)
- **ماښام**: ۷:۰۰ - ۱۰:۰۰ (۳ ساعته)

### د اونۍ ټولیز کار: ۶۰-۷۰ ساعته

---

## د ګړندۍ پرمختګ لپاره مشورې (Tips for Fast Development)

1. **د چمتو شویو کتابتونونو کارول**
   - Ant Design د UI لپاره
   - Sequelize د ډیټابیس لپاره
   - Express.js د API لپاره

2. **د کوډ د بیا کارولو ستراتیژي**
   - د عمومي کمپوننټونو جوړول
   - د API د عمومي فنکشنونو جوړول

3. **د ټیسټ ستراتیژي**
   - د هرې ورځې په پای کې ټیسټ
   - د اصلي فیچرونو لومړیتوب

4. **د وخت مدیریت**
   - د هرې ورځې د کار لیست
   - د ورځني موخو ټاکل

---

دا پلان ستاسو سره مرسته کوي چې په ۱۸ ورځو کې د صرافۍ سیسټم بشپړ کړئ. که ستاسو کومه پوښتنه وي یا د کوم ځانګړي برخې د تفصیلاتو ته اړتیا لرئ، زه دلته یم چې ستاسو سره مرسته وکړم.
