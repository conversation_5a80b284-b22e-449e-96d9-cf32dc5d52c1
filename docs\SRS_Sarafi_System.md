# Software Requirements Specification (SRS)
## Sarafi Money Exchange System

### Document Information
- **Project Name**: Sarafi Money Exchange System
- **Version**: 1.0
- **Date**: August 6, 2025
- **Prepared by**: Development Team

---

## 1. Introduction

### 1.1 Purpose
This document specifies the requirements for the Sarafi Money Exchange System, a comprehensive financial management application designed for money exchange businesses.

### 1.2 Scope
The Sarafi system will manage:
- Customer registration and management
- Currency exchange operations
- Daily transaction recording (Daybook/Roznamcha)
- Customer ledger management
- User administration and permissions
- System backup and maintenance

### 1.3 Definitions and Acronyms
- **Jamah**: Credit entries (incoming money)
- **Naam**: Debit entries (outgoing money)
- **Roznamcha**: Daily transaction book
- **Kata**: Customer ledger
- **AFN**: Afghan Afghani currency
- **SRS**: Software Requirements Specification

---

## 2. Overall Description

### 2.1 Product Perspective
The Sarafi system is a standalone desktop/web application designed for money exchange businesses to manage their daily operations, customer relationships, and financial records.

### 2.2 Product Functions
1. **User Management**: Multi-level user access with role-based permissions
2. **Customer Management**: Registration, editing, and tracking of customer information
3. **Currency Management**: Support for multiple currencies with visual coding
4. **Transaction Recording**: Dual-entry bookkeeping system (Jamah/Naam)
5. **Ledger Management**: Customer-wise transaction history and balances
6. **Reporting**: Transaction summaries, customer statements, and financial reports
7. **System Administration**: Backup, restore, and maintenance functions

### 2.3 User Classes
1. **Super Admin**: Full system access and user management
2. **Regular Users**: Limited access based on assigned permissions
3. **Customers**: External entities whose transactions are recorded

---

## 3. System Features

### 3.1 Application Registration
**Description**: Initial system setup and business registration

**Functional Requirements**:
- FR-1.1: System shall store responsible person details
- FR-1.2: System shall store business mobile number
- FR-1.3: System shall support company logo upload
- FR-1.4: System shall store company name and address

### 3.2 User Management (Super Admin Only)
**Description**: User account creation and management

**Functional Requirements**:
- FR-2.1: Super Admin can create new user accounts
- FR-2.2: System shall store user name, mobile, username, password
- FR-2.3: Super Admin can assign feature permissions to users
- FR-2.4: Super Admin can edit user details
- FR-2.5: Super Admin can delete user accounts
- FR-2.6: System shall maintain user activity logs

### 3.3 Customer Registration
**Description**: Customer information management

**Functional Requirements**:
- FR-3.1: System shall auto-generate unique Customer ID
- FR-3.2: System shall store customer name, mobile, address
- FR-3.3: System shall set customer loan limits (max 500,000 AFN)
- FR-3.4: Users can edit customer information
- FR-3.5: Users can delete customer records
- FR-3.6: System shall support customer record printing

### 3.4 Currency Management
**Description**: Multi-currency support system

**Functional Requirements**:
- FR-4.1: System shall store currency name and code
- FR-4.2: System shall assign color codes for visual distinction
- FR-4.3: System shall support currency editing and deletion
- FR-4.4: System shall validate currency codes for uniqueness

### 3.5 Daybook (Roznamcha) System
**Description**: Daily transaction recording with dual-entry system

**Functional Requirements**:
- FR-5.1: System shall provide split interface (Jamah/Naam)
- FR-5.2: System shall auto-generate unique transaction IDs
- FR-5.3: System shall record registration date and creator
- FR-5.4: System shall auto-increment page numbers
- FR-5.5: System shall support multiple currency transactions
- FR-5.6: System shall calculate real-time balances
- FR-5.7: System shall provide save, cancel, and finalize options
- FR-5.8: System shall lock finalized daybooks (admin-only unlock)
- FR-5.9: System shall support transaction search and filtering
- FR-5.10: System shall require password verification for edits

### 3.6 Ledger (Kata) System
**Description**: Customer-wise transaction summary and history

**Functional Requirements**:
- FR-6.1: System shall display customer transaction history
- FR-6.2: System shall calculate customer balances per currency
- FR-6.3: System shall support filtering by customer/currency
- FR-6.4: System shall provide grand totals for Jamah/Naam
- FR-6.5: System shall support PDF export and printing
- FR-6.6: System shall link to detailed transaction records

### 3.7 System Administration
**Description**: Backup, maintenance, and system management

**Functional Requirements**:
- FR-7.1: System shall provide database backup functionality
- FR-7.2: System shall support database restoration
- FR-7.3: System shall allow selective data deletion
- FR-7.4: System shall provide system reset to zero-state
- FR-7.5: System shall maintain audit logs for admin actions

---

## 4. Non-Functional Requirements

### 4.1 Performance Requirements
- NFR-1.1: System shall support up to 10,000 customers
- NFR-1.2: Transaction recording shall complete within 2 seconds
- NFR-1.3: Report generation shall complete within 10 seconds

### 4.2 Security Requirements
- NFR-2.1: System shall use encrypted password storage
- NFR-2.2: System shall implement role-based access control
- NFR-2.3: System shall log all user activities
- NFR-2.4: System shall require authentication for sensitive operations

### 4.3 Usability Requirements
- NFR-3.1: System shall provide intuitive user interface
- NFR-3.2: System shall support both English and Pashto languages
- NFR-3.3: System shall provide color-coded currency identification

### 4.4 Reliability Requirements
- NFR-4.1: System shall have 99.9% uptime during business hours
- NFR-4.2: System shall automatically backup data daily
- NFR-4.3: System shall recover from failures within 5 minutes

---

## 5. System Interfaces

### 5.1 User Interfaces
- Desktop application with modern GUI
- Responsive design for different screen sizes
- Multi-language support (English/Pashto)

### 5.2 Hardware Interfaces
- Standard PC/laptop hardware
- Printer support for reports and receipts
- Network connectivity for multi-user access

### 5.3 Software Interfaces
- Database management system
- PDF generation library
- Backup/restore utilities

---

## 6. Data Requirements

### 6.1 Data Storage
- Customer information and transaction history
- User accounts and permissions
- Currency definitions and exchange rates
- System configuration and audit logs

### 6.2 Data Backup
- Daily automated backups
- Manual backup on-demand
- Backup retention for 1 year minimum

---

## 7. Constraints

### 7.1 Technical Constraints
- Must work on Windows operating system
- Database size limited by available storage
- Network dependency for multi-user access

### 7.2 Business Constraints
- Must comply with local financial regulations
- Must support Afghan business practices
- Must handle multiple currencies simultaneously

---

## 8. Assumptions and Dependencies

### 8.1 Assumptions
- Users have basic computer literacy
- Stable power supply during operations
- Regular system maintenance will be performed

### 8.2 Dependencies
- Database management system availability
- Operating system compatibility
- Hardware reliability

---

## 9. Approval

This SRS document requires approval from:
- Business Stakeholders
- Development Team Lead
- System Administrator
- End Users Representatives

---

**Document Status**: Draft
**Next Review Date**: [To be determined]
**Approval Date**: [Pending]
